# Train, validation, and test dataset paths
train: /root/autodl-tmp/yolov5-master/dataset/yqjdataset/train/images # 可以使用单独的验证集
test: /root/autodl-tmp/yolov5-master/dataset/yqjdataset/test/images

# Number of classes
nc: 47  # 替换为你的类别数量

# Class names
names: ['0', '1', '2', '3', '4', '5', '6', ' 7 ', ' 8 ', '9','10','11 ','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37','38','39','40','41','42','43','44','45','46']  # 替换为你的类别名称




# names: [
#   "CST_01", "CST_02", "CST_03",
#   "MTF_01", "MTF_02", "MTF_03", "MTF_04", "MTF_05", "MTF_06", "MTF_07", "MTF_08", "MTF_09", "MTF_11",
#   "EXC_01",
#   "CTF_01", "CTF_02", "CTF_03", "CTF_04", "CTF_05", "CTF_06", "CTF_07", "CTF_08", "CTF_09",
#   "REA_01",
#   "CB_K01", "DC_S01", "LD_S01", "QCS_01", "FS_L01", "GR_T01", "CAP_01", "CG_D01", "HC_T01", "HBK_01", "GT_F01",
#   "RC_A01", "DC_G01", "OMA_01", "SED_01", "DCC_01", "FTS_01", "FDS_01", "GND_01", "AMM_01", "VOL_01", "LAR_01",
#   "coil"
# ]
