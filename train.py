import warnings

warnings.filterwarnings('ignore')
from ultralytics import YOLO
import torch


def train_model():
    # 检查GPU可用性
    device = '0' if torch.cuda.is_available() else 'cpu'

    # 加载预训练模型
    model = YOLO(r'yolo11s.pt')

    # 训练参数配置
    model.train(
        data=r'yqjdataset\data.yaml',
        epochs=100,
        imgsz=640,
        batch=16,
        device=device,
        optimizer='AdamW',
        lr0=0.001,
        close_mosaic=15,
        workers=4,
        amp=True,  # 自动混合精度
        single_cls=False,
        project='animal_detection1',
        name='yolo11s_exp1'
    )


if __name__ == '__main__':
    train_model()