import os
import torch
from ultralytics import YOLO
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from sklearn.metrics import classification_report, confusion_matrix, precision_recall_curve, average_precision_score
import numpy as np
import matplotlib

# 设置全局字体为黑体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
# 设置正常显示负号
matplotlib.rcParams['axes.unicode_minus'] = False
# 模型和数据集路径
model_path = r'D:\PAchongtext\中医药识别优化系统\animal_detection1\yolo11s_exp1\weights\best.pt'
data_dir = r'D:\PAchongtext\中医药识别优化系统\中草药检测数据集（已整理中文标签）\valid\images'

# 加载模型
model = YOLO(model_path)
device = '0' if torch.cuda.is_available() else 'cpu'

# 中草药类别中文名称
chinese_names = [
    "白茯苓", "白芍", "白术", "蒲公英", "甘草", "栀子", "党参", "桃仁", "去皮桃仁", "地肤子",
    "牡丹皮", "冬虫夏草", "杜仲", "当归", "杏仁", "何首乌", "黄精", "鸡血藤", "枸杞", "莲须",
    "莲肉", "麦门冬", "木通", "玉竹", "女贞子", "肉苁蓉", "人参", "乌梅", "覆盆子", "瓜蒌皮",
    "肉桂", "山茱萸", "山药", "酸枣仁", "桑白皮", "山楂", "天麻", "熟地黄", "小茴香", "泽泻",
    "竹茹", "川贝母", "川芎", "玄参", "益智仁"
]

# 收集所有图片路径
image_paths = [os.path.join(data_dir, f) for f in os.listdir(data_dir) if f.endswith(('.jpg', '.png', '.jpeg'))]

# 评估函数
def evaluate_model():
    true_labels = []
    pred_labels = []
    confidences = []
    
    for img_path in image_paths:
        # 从对应的标签文件获取真实类别
        img_name = os.path.splitext(os.path.basename(img_path))[0]
        label_path = os.path.join(r'D:\PAchongtext\中医药识别优化系统\中草药检测数据集（已整理中文标签）\valid\labels', f'{img_name}.txt')
        
        # 读取标签文件中的类别
        try:
            with open(label_path, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                if first_line:
                    true_class_idx = int(first_line.split()[0])
                    true_labels.append(true_class_idx)
                else:
                    # 如果标签文件为空，跳过该图片
                    continue
        except FileNotFoundError:
            # 如果标签文件不存在，跳过该图片
            continue
        
        # 模型预测
        results = model.predict(img_path, device=device)
        
        # 处理预测结果 - 只取置信度最高的预测
        if len(results[0].boxes) > 0:
            best_box = max(results[0].boxes, key=lambda box: float(box.conf))
            pred_labels.append(int(best_box.cls))
            confidences.append(float(best_box.conf))
        else:
            # 如果没有预测结果，使用-1作为占位符
            pred_labels.append(-1)
            confidences.append(0.0)
    
    # 确保真实标签和预测标签数量一致
    assert len(true_labels) == len(pred_labels), f"标签数量不匹配: 真实标签={len(true_labels)}, 预测标签={len(pred_labels)}"
    
    # 过滤掉预测为-1的样本
    filtered_true = [true for true, pred in zip(true_labels, pred_labels) if pred != -1]
    filtered_pred = [pred for pred in pred_labels if pred != -1]
    filtered_conf = [conf for conf, pred in zip(confidences, pred_labels) if pred != -1]
    
    if not filtered_true:
        print("警告：所有预测结果均为空！")
        return
        
    # 计算评估指标
    report = classification_report(filtered_true, filtered_pred, target_names=chinese_names, output_dict=True, digits=4)
    
    # 混淆矩阵
    cm = confusion_matrix(filtered_true, filtered_pred)
    
    # 多分类PR曲线 - 每个类别单独计算
    precision = dict()
    recall = dict()
    ap = dict()
    for i in range(len(chinese_names)):
        # 为当前类别创建二分类标签
        y_true = [1 if label == i else 0 for label in filtered_true]
        y_score = [conf if label == i else 0 for label, conf in zip(filtered_pred, filtered_conf)]
        
        # 计算当前类别的PR曲线
        precision[i], recall[i], _ = precision_recall_curve(y_true, y_score)
        ap[i] = average_precision_score(y_true, y_score)
    
    # 计算macro平均AP
    ap['macro'] = np.mean(list(ap.values()))
    
    # 可视化
    # 1. 混淆矩阵
    plt.figure(figsize=(15, 12))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=chinese_names, yticklabels=chinese_names)
    plt.title('混淆矩阵')
    plt.savefig('confusion_matrix.png')
    plt.close()
    
    # 2. PR曲线
    plt.figure(figsize=(10, 8))
    for i in range(len(chinese_names)):
        plt.plot(recall[i], precision[i], lw=2, label=f'{chinese_names[i]} (AP={ap[i]:.2f})')
    plt.xlabel('召回率')
    plt.ylabel('精确率')
    plt.title('精确率-召回率曲线(按类别)')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.savefig('pr_curve.png')
    plt.close()
    
    # 3. ROC曲线
    from sklearn.metrics import roc_curve, auc
    plt.figure(figsize=(10, 8))
    for i in range(len(chinese_names)):
        y_true = [1 if label == i else 0 for label in filtered_true]
        y_score = [conf if label == i else 0 for label, conf in zip(filtered_pred, filtered_conf)]
        fpr, tpr, _ = roc_curve(y_true, y_score)
        roc_auc = auc(fpr, tpr)
        plt.plot(fpr, tpr, lw=2, label=f'{chinese_names[i]} (AUC={roc_auc:.2f})')
    plt.plot([0, 1], [0, 1], 'k--', lw=2)
    plt.xlabel('假阳性率')
    plt.ylabel('真阳性率')
    plt.title('ROC曲线(按类别)')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.savefig('roc_curve.png')
    plt.close()
    
    # 4. 类别分布直方图
    plt.figure(figsize=(12, 6))
    pd.Series(true_labels).value_counts().sort_index().plot(kind='bar')
    plt.xticks(range(len(chinese_names)), chinese_names, rotation=90)
    plt.title('类别分布')
    plt.xlabel('类别')
    plt.ylabel('数量')
    plt.tight_layout()
    plt.savefig('class_distribution.png')
    plt.close()
    
    # 5. 置信度分布图
    plt.figure(figsize=(10, 6))
    plt.hist(confidences, bins=20, edgecolor='black')
    plt.title('置信度分布')
    plt.xlabel('置信度分数')
    plt.ylabel('数量')
    plt.savefig('confidence_distribution.png')
    plt.close()
    
    # 6. 错误分析图（预测错误的样本）
    error_indices = [i for i, (true, pred) in enumerate(zip(filtered_true, filtered_pred)) if true != pred]
    error_conf = [conf for i, conf in enumerate(filtered_conf) if i in error_indices]
    plt.figure(figsize=(10, 6))
    plt.hist(error_conf, bins=20, edgecolor='black')
    plt.title('错误分析: 错误预测的置信度')
    plt.xlabel('置信度分数')
    plt.ylabel('数量')
    plt.savefig('error_analysis.png')
    plt.close()
    
    # 7. 各类别准确率柱状图
    class_acc = {}
    for i in range(len(chinese_names)):
        correct = sum(1 for true, pred in zip(filtered_true, filtered_pred) if true == i and pred == i)
        total = sum(1 for true in filtered_true if true == i)
        class_acc[chinese_names[i]] = correct / total if total > 0 else 0
    
    plt.figure(figsize=(12, 6))
    plt.bar(class_acc.keys(), class_acc.values())
    plt.xticks(rotation=90)
    plt.title('各类别准确率')
    plt.xlabel('类别')
    plt.ylabel('准确率')
    plt.ylim(0, 1)
    plt.tight_layout()
    plt.savefig('class_accuracy.png')
    plt.close()
    

    
  
    
    # 9. 各类别平均置信度
    class_conf = {}
    for i in range(len(chinese_names)):
        confs = [conf for label, conf in zip(filtered_pred, filtered_conf) if label == i]
        class_conf[chinese_names[i]] = np.mean(confs) if confs else 0
    
    plt.figure(figsize=(12, 6))
    plt.bar(class_conf.keys(), class_conf.values())
    plt.xticks(rotation=90)
    plt.title('各类别平均置信度')
    plt.xlabel('类别')
    plt.ylabel('平均置信度')
    plt.ylim(0, 1)
    plt.tight_layout()
    plt.savefig('class_confidence.png')
    plt.close()
    
    # 生成评估报告
    metrics = {
        '平均精确率(mAP)': ap,
        '精确率': report['macro avg']['precision'],
        '召回率': report['macro avg']['recall'],
        'F1分数': report['macro avg']['f1-score'],
        '准确率': report['accuracy'],
        '各类别精确率': {chinese_names[i]: report.get(str(i), {}).get('precision', 0) for i in range(len(chinese_names))},
        '各类别召回率': {chinese_names[i]: report.get(str(i), {}).get('recall', 0) for i in range(len(chinese_names))},
        '各类别F1分数': {chinese_names[i]: report.get(str(i), {}).get('f1-score', 0) for i in range(len(chinese_names))},
        '混淆矩阵': cm.tolist(),
        'PR曲线数据': {
            '按类别': {
                chinese_names[i]: {'精确率': precision[i].tolist(), '召回率': recall[i].tolist(), '平均精确率': ap[i]}
                for i in range(len(chinese_names))
            },
            '宏观平均精确率': ap['macro']
        },
        '平均置信度': np.mean(confidences),
        '最小置信度': np.min(confidences),
        '最大置信度': np.max(confidences),
        '类别分布': pd.Series(true_labels).value_counts().to_dict(),
        '推理时间': results[0].speed['inference'],
        '预处理时间': results[0].speed['preprocess'],
        '后处理时间': results[0].speed['postprocess']
    }
    
    # 保存报告
    with open('evaluation_report.json', 'w', encoding='utf-8') as f:
        import json
        json.dump(metrics, f, ensure_ascii=False, indent=4)
    
    # 打印格式化的分类报告
    print("分类报告:")
    report_text = classification_report(filtered_true, filtered_pred, target_names=chinese_names, digits=4)
    print(report_text)
    
    # 将分类报告保存为txt文件
    with open('classification_report1.txt', 'w', encoding='utf-8') as f:
        f.write("分类报告:\n")
        f.write(report_text)
        f.write("\n\n各类别评估指标:\n")
        for i in range(len(chinese_names)):
            if str(i) in report:
                f.write(f"{chinese_names[i]}: 精确率={report[str(i)]['precision']:.4f}, 召回率={report[str(i)]['recall']:.4f}, F1分数={report[str(i)]['f1-score']:.4f}\n")
        f.write(f"\n宏观平均: 精确率={report['macro avg']['precision']:.4f}, 召回率={report['macro avg']['recall']:.4f}, F1分数={report['macro avg']['f1-score']:.4f}\n")
        f.write(f"准确率: {report['accuracy']:.4f}\n")
    
    # 打印各类别评估指标
    print("\n各类别评估指标:")
    for i in range(len(chinese_names)):
        if str(i) in report:
            print(f"{chinese_names[i]}: 精确率={report[str(i)]['precision']:.4f}, 召回率={report[str(i)]['recall']:.4f}, F1分数={report[str(i)]['f1-score']:.4f}")
    
    print(f"\n宏观平均: 精确率={report['macro avg']['precision']:.4f}, 召回率={report['macro avg']['recall']:.4f}, F1分数={report['macro avg']['f1-score']:.4f}")
    print(f"准确率: {report['accuracy']:.4f}")
    
    return metrics

if __name__ == "__main__":
    evaluate_model()